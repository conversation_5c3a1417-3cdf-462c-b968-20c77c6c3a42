import { z } from "zod";
import { listAllEntitiesQuerySchema } from "../schema/zod-common.schema.js";
import { PgTransaction } from "drizzle-orm/pg-core";
import { PostgresJsQueryResultHKT } from "drizzle-orm/postgres-js";
import * as schema from "../../db/schemas/index.js";
import { ExtractTablesWithRelations } from "drizzle-orm/relations";

export type ListAllEntitiesQueryOptions = z.infer<
  typeof listAllEntitiesQuerySchema
>;

export interface ServiceOptions {
  pgTrx?: PgTransaction<
    PostgresJsQueryResultHKT,
    typeof schema,
    ExtractTablesWithRelations<typeof schema>
  >;
}

export interface PaginatedApiResponse<T> {
  items: T[];
  total: number | string | bigint;
}

export interface EntityId {
  id: string;
}
