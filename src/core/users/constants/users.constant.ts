import { PasswordService } from "../../../shared/services/password.service.js";
import { NewUser } from "../types/users.types.js";

export const PLATFORM_ADMIN: Omit<NewUser, "roleId"> = {
  name: process.env.PLATFORM_ADMIN_NAME ?? "Admin",
  email: process.env.PLATFORM_ADMIN_EMAIL ?? "<EMAIL>",
  password: new PasswordService().hashSync(
    process.env.PLATFORM_ADMIN_PASSWORD ?? "Password0011$",
  ),
  phone: process.env.PLATFORM_ADMIN_PHONE ?? "1234567890",
  address: process.env.PLATFORM_ADMIN_ADDRESS ?? "Chakwal, Pakistan",
  gender:
    (process.env.PLATFORM_ADMIN_GENDER as NewUser["gender"] | undefined) ??
    "MALE",
  cnic: process.env.PLATFORM_ADMIN_CNIC ?? "1234567890123",
};
