import { createGuardianSchema } from "../dto/guardian.dto.js";
import { z } from "zod";
import { UserProfile } from "../../../../core/users/types/users.types.js";
import { guardianTable } from "../../../../db/schemas/index.js";

export type Guardian = typeof guardianTable.$inferSelect;
export type NewGuardian = typeof guardianTable.$inferInsert;
export type GuardianUpdate = Partial<NewGuardian>;

export type CreateGuardianPayload = z.infer<typeof createGuardianSchema>;

export type GuardianProfile = UserProfile & {
  relation: Guardian["relation"];
};
