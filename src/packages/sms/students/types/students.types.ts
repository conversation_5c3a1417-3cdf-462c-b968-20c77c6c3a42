import { Insertable, Selectable, Updateable } from "kysely";
import { createStudentSchema } from "../dto/students.dto.js";
import { z } from "zod";
import { studentTable } from "../../../../db/schemas/student.schema.js";

export type Student = typeof studentTable.$inferSelect;
export type NewStudent = typeof studentTable.$inferInsert;
export type StudentUpdate = Partial<NewStudent>;

export type CreateStudentPayload = z.infer<typeof createStudentSchema> & {
  academicSessionId: string;
};
