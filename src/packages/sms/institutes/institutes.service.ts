import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";

import { OwnersService } from "../institute-owners/owners.service.js";
import {
  FindOwnerInstituteResponse,
  Institute,
  InstituteUpdate,
  NewInstitute,
} from "./types/institute.types.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../utils/pg-utils.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../db/type.js";
import { and, desc, eq, SQL } from "drizzle-orm";
import { instituteTable } from "../../../db/schemas/index.js";

@Injectable()
export class InstitutesService {
  private readonly logger = new Logger(InstitutesService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly ownersService: OwnersService,
  ) {}

  public async findAll({
    limit,
    offset,
  }: ListAllEntitiesQueryOptions): Promise<Institute[]> {
    try {
      return await this.db.query.instituteTable.findMany({
        limit,
        offset,
        orderBy: desc(instituteTable.createdAt),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to get all institutes", error);
      throw error;
    }
  }

  public async findOwnerInstitute(
    ownerId: Institute["ownerId"],
  ): Promise<FindOwnerInstituteResponse> {
    try {
      const existingInstitute = await this.findOne({ ownerId });
      return { institute: existingInstitute ?? null };
    } catch (error: unknown) {
      this.logger.error("Failed to get owner institute", error);
      throw error;
    }
  }

  public async updateOwnerInstitute(
    ownerId: Institute["ownerId"],
    payload: Omit<InstituteUpdate, "ownerId" | "createdAt">,
  ) {
    const { id: instituteId } = await this.findOwnerInstituteOrThrow(ownerId);

    try {
      return await this.update(instituteId, payload);
    } catch (error: unknown) {
      this.logger.error("Failed to update owner institute", error);
      throw error;
    }
  }

  public async findById(id: Institute["id"]): Promise<Institute | undefined> {
    try {
      return await this.db.query.instituteTable.findFirst({
        where: eq(instituteTable.id, id),
      });
    } catch (error: unknown) {
      this.logger.error(`Failed to find institute by id: ${id}`, error);
      throw error;
    }
  }

  public async create(
    data: Omit<NewInstitute, "ownerId">,
    userId: string,
  ): Promise<Institute> {
    const existingOwner = await this.findOwnerOrThrow(userId);
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .insert(instituteTable)
          .values({
            ...data,
            ownerId: existingOwner.id,
          })
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to create institute", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "institute",
        logger: this.logger,
      });
    }
  }

  public async update(
    id: Institute["id"],
    data: Omit<InstituteUpdate, "ownerId" | "createdAt">,
  ): Promise<Institute> {
    try {
      return await executeQueryTakeFirstOrThrow(
        this.db
          .update(instituteTable)
          .set(data)
          .where(eq(instituteTable.id, id))
          .returning(),
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update institute", error);
      throw error;
    }
  }

  private async findOne(
    criteria: Partial<Pick<Institute, "name" | "id" | "ownerId">>,
  ) {
    try {
      return await this.db.query.instituteTable.findFirst({
        where: and(...this.createQueryFilters(criteria)),
      });
    } catch (error: unknown) {
      this.logger.error("Failed to find institute", error);
      throw error;
    }
  }

  private async findOwnerInstituteOrThrow(ownerId: Institute["ownerId"]) {
    let institute: Institute | undefined;
    try {
      institute = await this.findOne({ ownerId });
    } catch (error: unknown) {
      this.logger.error("Failed to verify owner institute exists", error);
      throw error;
    }

    if (!institute) {
      throw new NotFoundException("Owner institute not found");
    }
    return institute;
  }

  private async findOwnerOrThrow(userId: string) {
    let owner;

    try {
      owner = await this.ownersService.findUniqueOwner({ userId });
    } catch (error: unknown) {
      this.logger.error("Failed to check if owner exists", error);
      throw error;
    }

    if (!owner) {
      throw new NotFoundException(`Owner with doesn't  exist`);
    }

    return owner;
  }

  private createQueryFilters(
    criteria?: Partial<Pick<Institute, "name" | "id" | "ownerId">>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.name) {
      filters.push(eq(instituteTable.name, criteria.name));
    }
    if (criteria?.id) {
      filters.push(eq(instituteTable.id, criteria.id));
    }

    if (criteria?.ownerId) {
      filters.push(eq(instituteTable.ownerId, criteria.ownerId));
    }

    return filters;
  }
}
