import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectDrizzle } from "../../../../../shared/modules/drizzle/drizzle.decorators.js";
import type { Database } from "../../../../../db/type.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../../../utils/pg-utils.js";
import {
  classSectionExamTable,
  classSectionTable,
  classTable,
  enrollmentTable,
  examResultTable,
  examScheduleTable,
  examTable,
  sectionSubjectTable,
  studentTable,
  subjectTable,
} from "../../../../../db/schemas/index.js";
import {
  ExamResult,
  ExamResultResponse,
  NewExamResult,
} from "./types/exam-results.type.js";
import { ExamSchedulesService } from "../exam-schedules/exam-schedules.service.js";
import { EnrollmentService } from "../../../students/enrollments/enrollments.service.js";
import { StaffService } from "../../../staff/staff.service.js";
import { ExamScheduleResponse } from "../exam-schedules/types/exam-schedules.type.js";
import { and, eq, sql, SQL } from "drizzle-orm";
import {
  getSelectClassSectionSqlQuery,
  getSelectSubjectQuery,
} from "../../../../../utils/drizzle-raw-sql-queries.utils.js";
import {
  PaginatedApiResponse,
  ServiceOptions,
} from "../../../../../shared/types/shared.types.js";

@Injectable()
export class ExamResultsService {
  private readonly logger = new Logger(ExamResultsService.name);

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly examSchedulesService: ExamSchedulesService,
    private readonly staffService: StaffService,
    private readonly enrollmentsService: EnrollmentService,
  ) {}

  public async create(newExamResultData: NewExamResult) {
    try {
      const schedule = await this.examSchedulesService.findById(
        newExamResultData.examScheduleId,
      );
      if (!schedule) {
        throw new NotFoundException("Exam schedule not found");
      }

      await this.verifyStudentEnrollmentInScheduledExamClassSection(
        newExamResultData.enrollmentId,
        schedule.classSection.id,
      );

      await this.verifyCreatorHasGivenAccessToCreateScheduledExamResult(
        newExamResultData.createdBy,
        schedule,
      );

      return await this.db.transaction(async trx => {
        const { id } = await executeQueryTakeFirstOrThrow(
          trx
            .insert(examResultTable)
            .values(newExamResultData)
            .returning({ id: examResultTable.id }),
        );

        return await this.findFirstOrThrow({ id }, { pgTrx: trx });
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create exam result", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "examResult",
      });
    }
  }

  // public async createInBulk(newExamResultData: NewExamResult[]) {
  //   try {

  //   } catch (error: unknown) {
  //     this.logger.error("Failed to create exam results in bulk", error);
  //     handleDatabaseInsertException(error, {
  //       resource: "examResult",
  //     });
  //   }
  // }

  public async findClassSectionResults(classSectionId: string) {
    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();

    const {
      query: selectSubjectQuery,
      subjectTeacherUserAlias,
      subjectTeacherStaffAlias,
    } = getSelectSubjectQuery();
    try {
      return await this.db
        .select({
          totalPassStudents: sql<number>`(count(*) FILTER (WHERE ${examResultTable.isAbsent} = false AND ${examResultTable.marksObtained} >= ${examScheduleTable.passingMarks}))::int`,
          totalFailStudents: sql<number>`(count(*) FILTER (WHERE ${examResultTable.isAbsent} = false AND ${examResultTable.marksObtained} < ${examScheduleTable.passingMarks}))::int`,
          totalPresent: sql<number>`(count(*) FILTER (WHERE ${examResultTable.isAbsent} = false))::int`,
          totalAbsent: sql<number>`(count(*) FILTER (WHERE ${examResultTable.isAbsent} = true))::int`,
          examName: examTable.name,
          totalMarks: examScheduleTable.totalMarks,
          classSection: selectClassSectionQuery,
          subject: selectSubjectQuery,
        })
        .from(examResultTable)
        .innerJoin(
          examScheduleTable,
          eq(examScheduleTable.id, examResultTable.examScheduleId),
        )
        .innerJoin(
          enrollmentTable,
          eq(enrollmentTable.id, examResultTable.enrollmentId),
        )
        .innerJoin(studentTable, eq(studentTable.id, enrollmentTable.studentId))
        .innerJoin(
          classSectionExamTable,
          eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
        )
        .innerJoin(examTable, eq(examTable.id, classSectionExamTable.examId))
        .innerJoin(
          classSectionTable,
          eq(classSectionTable.id, classSectionExamTable.classSectionId),
        )
        .innerJoin(
          classTeacherStaffAlias,
          eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
        )
        .innerJoin(
          classTeacherUserAlias,
          eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
        )
        .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
        .innerJoin(
          sectionSubjectTable,
          eq(sectionSubjectTable.id, examScheduleTable.sectionSubjectId),
        )
        .innerJoin(
          subjectTable,
          eq(subjectTable.id, sectionSubjectTable.subjectId),
        )
        .innerJoin(
          subjectTeacherStaffAlias,
          eq(subjectTeacherStaffAlias.id, sectionSubjectTable.subjectTeacherId),
        )
        .innerJoin(
          subjectTeacherUserAlias,
          eq(subjectTeacherUserAlias.id, subjectTeacherStaffAlias.userId),
        )
        .groupBy(
          classSectionTable.id,
          classTable.id,
          classSectionTable.classTeacherId,
          classTeacherStaffAlias.id,
          classTeacherUserAlias.id,
          examTable.id,
          examScheduleTable.id,
          sectionSubjectTable.id,
          subjectTable.id,
          subjectTeacherStaffAlias.id,
          subjectTeacherUserAlias.id,
        )
        .where(eq(classSectionTable.id, classSectionId));
    } catch (error: unknown) {
      this.logger.error("Failed to find class section results", error);
      throw error;
    }
  }
  public async findAll(
    criteria?: Partial<Pick<ExamResult, "examScheduleId">>,
  ): Promise<PaginatedApiResponse<ExamResultResponse>> {
    try {
      const [items, total] = await Promise.all([
        this.buildSelectQuery(this.buildQueryFilters(criteria)).execute(),
        this.db.$count(
          examResultTable,
          and(...this.buildQueryFilters(criteria)),
        ),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all exam results", error);
      throw error;
    }
  }

  public async findFirstOrThrow(
    criteria: Partial<ExamResult>,
    options?: ServiceOptions,
  ): Promise<ExamResultResponse> {
    let existingExamResult: ExamResultResponse | undefined;
    try {
      existingExamResult = (
        await this.buildSelectQuery(
          this.buildQueryFilters(criteria),
          options,
        ).execute()
      )[0];
    } catch (error: unknown) {
      this.logger.error("Failed to find exam result", error);
      throw error;
    }
    if (!existingExamResult) {
      throw new NotFoundException("Exam result not found");
    }
    return existingExamResult;
  }

  private async verifyStudentEnrollmentInScheduledExamClassSection(
    enrollmentId: string,
    classSectionId: string,
  ) {
    try {
      const enrollment = await this.enrollmentsService.findById(enrollmentId);
      if (!enrollment) {
        throw new NotFoundException("Enrollment not found");
      }

      if (enrollment.classSection.id !== classSectionId) {
        throw new BadRequestException(
          "Student's enrollment does not belong to the class section",
        );
      }
    } catch (error: unknown) {
      if (!(error instanceof HttpException)) {
        this.logger.error(
          "Failed to verify student enrollment in class section",
          error,
        );
      }
      throw error;
    }
  }

  private async verifyCreatorHasGivenAccessToCreateScheduledExamResult(
    teacherId: string,
    examSchedule: ExamScheduleResponse,
  ) {
    try {
      const staff =
        await this.staffService.findInstitutionalStaffById(teacherId);

      if (!staff) {
        throw new NotFoundException("Staff not found");
      }

      if (staff.type === "TEACHER") {
        if (
          staff.id !== examSchedule.subject.teacher.id ||
          staff.id !== examSchedule.classSection.classTeacher.id
        ) {
          throw new ForbiddenException(
            "You are not authorized to create this result",
          );
        }
      }
    } catch (error: unknown) {
      if (!(error instanceof HttpException)) {
        this.logger.error(
          "Failed to verify teacher has access to create exam result",
          error,
        );
      }
      throw error;
    }
  }

  /**
   * Updates an existing exam result
   * @param resultId - The exam result ID to update
   * @param updateData - The data to update
   * @param updatedBy - The ID of the user updating the result
   */
  public async update(
    resultId: string,
    updateData: Partial<
      Pick<ExamResult, "marksObtained" | "remarks" | "isAbsent">
    >,
    updatedBy: string,
  ) {
    try {
      // First, find the existing result to verify permissions
      const existingResult = await this.db.query.examResultTable.findFirst({
        where: eq(examResultTable.id, resultId),
      });

      if (!existingResult) {
        throw new NotFoundException("Exam result not found");
      }

      // Get the exam schedule to verify permissions
      const schedule = await this.examSchedulesService.findById(
        existingResult.examScheduleId,
      );

      if (!schedule) {
        throw new NotFoundException("Exam schedule not found");
      }

      // Verify the updater has permission to update this result
      await this.verifyCreatorHasGivenAccessToCreateScheduledExamResult(
        updatedBy,
        schedule,
      );

      // Update the result
      return await this.db.transaction(async trx => {
        const { id } = await executeQueryTakeFirstOrThrow(
          this.db
            .update(examResultTable)
            .set(updateData)
            .where(eq(examResultTable.id, resultId))
            .returning(),
        );

        return await this.findFirstOrThrow({ id }, { pgTrx: trx });
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update exam result", error);
      if (!(error instanceof HttpException)) {
        handleDatabaseInsertOrUpdateException(error, {
          resource: "examResult",
        });
      }
      throw error;
    }
  }

  private buildSelectQuery(filters: SQL[], options?: ServiceOptions) {
    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: examResultTable.id,
        subject: subjectTable.name,
        totalMarks: examScheduleTable.totalMarks,
        passingMarks: examScheduleTable.passingMarks,
        date: examScheduleTable.date,
        marksObtained: examResultTable.marksObtained,
        remarks: examResultTable.remarks,
        isAbsent: examResultTable.isAbsent,
        createdAt: examResultTable.createdAt,
        student: sql<ExamResultResponse["student"]>`json_build_object(
            'id', ${studentTable.id},
            'name', ${studentTable.name},
            'rollNumber', ${studentTable.rollNumber},
            'photo', ${studentTable.photo}
          )`,
        classSection: selectClassSectionQuery,
      })
      .from(examResultTable)
      .innerJoin(
        examScheduleTable,
        eq(examScheduleTable.id, examResultTable.examScheduleId),
      )
      .innerJoin(
        classSectionExamTable,
        eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
      )
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, classSectionExamTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .innerJoin(
        sectionSubjectTable,
        eq(sectionSubjectTable.id, examScheduleTable.sectionSubjectId),
      )
      .innerJoin(
        subjectTable,
        eq(subjectTable.id, sectionSubjectTable.subjectId),
      )
      .innerJoin(
        classTeacherStaffAlias,
        eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
      )
      .innerJoin(
        classTeacherUserAlias,
        eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
      )
      .innerJoin(
        enrollmentTable,
        eq(enrollmentTable.id, examResultTable.enrollmentId),
      )
      .innerJoin(studentTable, eq(studentTable.id, enrollmentTable.studentId))
      .where(and(...filters));
  }

  private buildQueryFilters(criteria?: Partial<ExamResult>) {
    const filters: SQL[] = [];

    if (criteria?.id) {
      filters.push(eq(examResultTable.id, criteria.id));
    }

    if (criteria?.examScheduleId) {
      filters.push(eq(examResultTable.examScheduleId, criteria.examScheduleId));
    }

    if (criteria?.enrollmentId) {
      filters.push(eq(examResultTable.enrollmentId, criteria.enrollmentId));
    }

    if (criteria?.isAbsent) {
      filters.push(eq(examResultTable.isAbsent, criteria.isAbsent));
    }

    if (criteria?.marksObtained) {
      filters.push(eq(examResultTable.marksObtained, criteria.marksObtained));
    }

    if (criteria?.remarks) {
      filters.push(eq(examResultTable.remarks, criteria.remarks));
    }

    if (criteria?.createdAt) {
      filters.push(eq(examResultTable.createdAt, criteria.createdAt));
    }

    if (criteria?.createdBy) {
      filters.push(eq(examResultTable.createdBy, criteria.createdBy));
    }

    return filters;
  }
}
