import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import type { Database } from "../../../../../db/type.js";
import { InjectDrizzle } from "../../../../../shared/modules/drizzle/drizzle.decorators.js";
import {
  ExamSchedule,
  ExamScheduleResponse,
  NewExamSchedule,
} from "./types/exam-schedules.type.js";
import {
  classTable,
  sectionSubjectTable,
  subjectTable,
  classSectionExamTable,
  classSectionTable,
  examScheduleTable,
  examTable,
  usersTable,
  staffTable,
} from "../../../../../db/schemas/index.js";
import {
  executeQueryTakeFirstOrThrow,
  handleDatabaseInsertOrUpdateException,
} from "../../../../../utils/pg-utils.js";
import { and, desc, eq, gte, lte, sql, SQL } from "drizzle-orm";
import {
  getSelectClassSectionSqlQuery,
  selectExamQuery,
} from "../../../../../utils/drizzle-raw-sql-queries.utils.js";
import { alias } from "drizzle-orm/pg-core";
import {
  PaginatedApiResponse,
  ServiceOptions,
} from "../../../../../shared/types/shared.types.js";
import { ListExamSchedulesQueryDto } from "./dto/list-exams-shedules-query.dto.js";

@Injectable()
export class ExamSchedulesService {
  private readonly logger = new Logger(ExamSchedulesService.name);
  public constructor(@InjectDrizzle() private readonly db: Database) {}

  public async create(
    newExamScheduleData: NewExamSchedule,
  ): Promise<ExamScheduleResponse> {
    try {
      return await this.db.transaction(async trx => {
        const { id } = await executeQueryTakeFirstOrThrow(
          trx
            .insert(examScheduleTable)
            .values(newExamScheduleData)
            .returning({ id: examScheduleTable.id }),
        );

        return await this.findFirstOrThrow({ id }, { pgTrx: trx });
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create exam schedule", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "examSchedule",
      });
    }
  }

  public async findById(
    id: ExamSchedule["id"],
  ): Promise<ExamScheduleResponse | undefined> {
    try {
      const res = await this.findAll({ id });
      return res.items[0];
    } catch (err) {
      this.logger.error("Failed to find exam schedule by id", err);
      throw err;
    }
  }

  public async findAll(
    criteria?: Partial<Pick<ExamSchedule, "id" | "classSectionExamId">> & {
      classSectionId?: string;
    },
    queryOptions?: ListExamSchedulesQueryDto,
  ): Promise<PaginatedApiResponse<ExamScheduleResponse>> {
    const filterOptions = {
      ...queryOptions,
      ...criteria,
    };
    try {
      const [items, total] = await Promise.all([
        this.buildSelectQuery(this.buildQueryFilters(filterOptions)),

        this.db
          .select({ count: sql<number>`count(*)::int`.as("count") })
          .from(examScheduleTable)
          .innerJoin(
            classSectionExamTable,
            eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
          )
          .innerJoin(
            classSectionTable,
            eq(classSectionTable.id, classSectionExamTable.classSectionId),
          )
          .where(and(...this.buildQueryFilters(filterOptions)))
          .then(rows => rows[0]?.count ?? 0),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all exam schedules", error);
      throw error;
    }
  }

  /**
   * Updates an existing exam schedule
   * @param scheduleId - The exam schedule ID to update
   * @param updateData - The data to update
   * @param _updatedBy - The ID of the user updating the schedule (for future use)
   */
  public async update(
    scheduleId: string,
    updateData: Partial<
      Pick<
        ExamSchedule,
        | "date"
        | "startTime"
        | "endTime"
        | "sectionSubjectId"
        | "totalMarks"
        | "passingMarks"
      >
    >,
    _updatedBy: string,
  ): Promise<ExamScheduleResponse> {
    try {
      // Update the schedule
      return await this.db.transaction(async trx => {
        const { id } = await executeQueryTakeFirstOrThrow(
          trx
            .update(examScheduleTable)
            .set(updateData)
            .where(eq(examScheduleTable.id, scheduleId))
            .returning({ id: examScheduleTable.id }),
        );

        return await this.findFirstOrThrow({ id }, { pgTrx: trx });
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update exam schedule", error);
      handleDatabaseInsertOrUpdateException(error, {
        resource: "examSchedule",
      });
    }
  }

  private buildSelectQuery(filters: SQL[], options?: ServiceOptions) {
    const subjectTeacherUserAlias = alias(usersTable, "subject_teacher_user");
    const subjectTeacherStaffAlias = alias(staffTable, "subject_teacher_staff");

    const {
      query: selectClassSectionQuery,
      classTeacherStaffAlias,
      classTeacherUserAlias,
    } = getSelectClassSectionSqlQuery();

    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        id: examScheduleTable.id,
        date: examScheduleTable.date,
        startTime: examScheduleTable.startTime,
        endTime: examScheduleTable.endTime,
        exam: selectExamQuery,
        classSection: selectClassSectionQuery,
        subject: sql<ExamScheduleResponse["subject"]>`json_build_object(
              'id', ${subjectTable.id},
              'name', ${subjectTable.name},
              'teacher', json_build_object(
                'id', ${subjectTeacherUserAlias.id},
                'name', ${subjectTeacherUserAlias.name},
                'photo', ${subjectTeacherUserAlias.photo}
              )
            )`,
        totalMarks: examScheduleTable.totalMarks,
        passingMarks: examScheduleTable.passingMarks,
      })
      .from(examScheduleTable)
      .innerJoin(
        classSectionExamTable,
        eq(classSectionExamTable.id, examScheduleTable.classSectionExamId),
      )
      .innerJoin(examTable, eq(examTable.id, classSectionExamTable.examId))
      .innerJoin(
        classSectionTable,
        eq(classSectionTable.id, classSectionExamTable.classSectionId),
      )
      .innerJoin(classTable, eq(classTable.id, classSectionTable.classId))
      .innerJoin(
        sectionSubjectTable,
        eq(sectionSubjectTable.id, examScheduleTable.sectionSubjectId),
      )
      .innerJoin(
        subjectTable,
        eq(subjectTable.id, sectionSubjectTable.subjectId),
      )
      .innerJoin(
        subjectTeacherStaffAlias,
        eq(subjectTeacherStaffAlias.id, sectionSubjectTable.subjectTeacherId),
      )
      .innerJoin(
        subjectTeacherUserAlias,
        eq(subjectTeacherUserAlias.id, subjectTeacherStaffAlias.userId),
      )
      .innerJoin(
        classTeacherStaffAlias,
        eq(classTeacherStaffAlias.id, classSectionTable.classTeacherId),
      )
      .innerJoin(
        classTeacherUserAlias,
        eq(classTeacherUserAlias.id, classTeacherStaffAlias.userId),
      )
      .where(and(...filters))
      .orderBy(desc(examScheduleTable.date));
  }

  public async findFirstOrThrow(
    criteria: Partial<ExamSchedule>,
    options?: ServiceOptions,
  ): Promise<ExamScheduleResponse> {
    try {
      const [item] = await this.buildSelectQuery(
        this.buildQueryFilters(criteria),
        options,
      ).execute();
      if (!item) {
        throw new NotFoundException("Exam schedule not found");
      }
      return item;
    } catch (error: unknown) {
      this.logger.error("Failed to find exam schedule", error);
      throw error;
    }
  }

  private buildQueryFilters(
    criteria?: Partial<Pick<ExamSchedule, "id" | "classSectionExamId">> & {
      classSectionId?: string;
    } & Partial<ListExamSchedulesQueryDto>,
  ) {
    const filters: SQL[] = [];

    if (criteria?.id) {
      filters.push(eq(examScheduleTable.id, criteria.id));
    }

    if (criteria?.classSectionId) {
      filters.push(eq(classSectionTable.id, criteria.classSectionId));
    }

    if (criteria?.date) {
      filters.push(gte(examScheduleTable.date, criteria.date));
    }

    if (criteria?.classSectionExamId) {
      filters.push(
        eq(examScheduleTable.classSectionExamId, criteria.classSectionExamId),
      );
    }

    if (criteria?.month) {
      const [year, month] = criteria.month.split("-");
      if (year && month) {
        filters.push(
          eq(sql`EXTRACT(YEAR FROM ${examScheduleTable.date})`, parseInt(year)),
          eq(
            sql`EXTRACT(MONTH FROM ${examScheduleTable.date})`,
            parseInt(month),
          ),
        );
      }
    }

    if (criteria?.from && criteria.to) {
      filters.push(
        gte(examScheduleTable.date, criteria.from),
        lte(examScheduleTable.date, criteria.to),
      );
    }

    return filters;
  }
}
