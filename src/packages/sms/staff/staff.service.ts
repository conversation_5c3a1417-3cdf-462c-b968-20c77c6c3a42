import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";

import {
  BranchAdminProfile,
  CreateInstitutionStaffPayload,
  CreateStaffPayload,
  ListStaffQueryOptions,
  Staff,
  StaffProfile,
  TeacherProfile,
  UpdateStaffPayload,
} from "./types/staff.types.js";
import {
  extractKeysFromObject,
  omitKeysFromObject,
} from "../../../utils/object-utils.js";
import { UsersService } from "../../../core/users/users.service.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { handleDatabaseInsertOrUpdateException } from "../../../utils/pg-utils.js";
import { BranchResponse } from "../branches/types/branches.types.js";
import { BranchesService } from "../branches/branches.service.js";
import { ModuleRef } from "@nestjs/core";
import {
  EntityId,
  PaginatedApiResponse,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { NewUser } from "../../../core/users/types/users.types.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { SectionSubjectsService } from "../sections-subject-assignment/section-subjects.service.js";
import { ClassSectionsService } from "../sections/class-sections.service.js";
import type { Database } from "../../../db/type.js";
import { InjectDrizzle } from "../../../shared/modules/drizzle/drizzle.decorators.js";
import { and, eq, sql } from "drizzle-orm";
import {
  branchTable,
  classSectionTable,
  roleTable,
  sectionSubjectTable,
  staffTable,
  supportStaffProfileTable,
  usersTable,
} from "../../../db/schemas/index.js";
import { SectionSubject } from "../sections-subject-assignment/types/section-subjects.type.js";

// TODO: Add better argument types for staff update methods and remove assertions

@Injectable()
export class StaffService implements OnModuleInit {
  private readonly logger = new Logger(StaffService.name);

  private userService: UsersService;
  private classSectionsService: ClassSectionsService;
  private academicSessionsService: AcademicSessionsService;
  private sectionSubjectsService: SectionSubjectsService;

  public constructor(
    @InjectDrizzle() private readonly db: Database,
    private readonly branchesService: BranchesService,
    private readonly rolesService: RolesService,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.userService = this.moduleRef.get(UsersService, {
      strict: false,
    });
    this.classSectionsService = this.moduleRef.get(ClassSectionsService, {
      strict: false,
    });

    this.academicSessionsService = this.moduleRef.get(AcademicSessionsService, {
      strict: false,
    });

    this.sectionSubjectsService = this.moduleRef.get(SectionSubjectsService, {
      strict: false,
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Find All Staff By Branch ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves all staff members associated with a specific
  | branch. It combines both institutional and support staff into a unified
  | result set using a UNION ALL query. The method supports pagination
  | through limit and offset parameters and returns staff sorted by
  | creation date.
  |
  */
  public async findAllByBranchId(
    branchId: string,
    queryOptions: ListStaffQueryOptions,
  ): Promise<PaginatedApiResponse<StaffProfile>> {
    try {
      // Build institutional staff query
      const institutionalStaffQuery =
        this.selectInstitutionalStaffQuery().where(
          eq(staffTable.branchId, branchId),
        );

      // Build support staff query
      const supportStaffQuery = this.selectSupportStaffQuery().where(
        eq(staffTable.branchId, branchId),
      );

      // For now, let's get both queries separately and combine them
      const [institutionalStaff, supportStaff] = await Promise.all([
        institutionalStaffQuery,
        supportStaffQuery,
      ]);

      // Combine results
      let allStaff = [...institutionalStaff, ...supportStaff];

      // Apply filters
      if (queryOptions.type) {
        allStaff = allStaff.filter(staff => staff.type === queryOptions.type);
      }

      // Sort by createdAt desc
      allStaff.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      // Apply pagination
      const total = allStaff.length;
      const items = allStaff.slice(
        queryOptions.offset,
        queryOptions.offset + queryOptions.limit,
      );

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Staff
  |--------------------------------------------------------------------------
  |
  | This method handles updating existing staff members in the system.
  | It determines the staff type (support or institutional) and delegates
  | the update operation to the appropriate specialized method. The process
  | includes validating the branch, finding the existing staff record,
  | and updating the relevant database tables within a transaction.
  |
  */
  public async update(
    id: Staff["id"],
    data: UpdateStaffPayload,
  ): Promise<EntityId> {
    await this.verifyBranchExists(data.branchId);

    const existingStaff = await this.findExistingStaffOrThrow(id);

    try {
      const staffInfo = extractKeysFromObject(data, ["salary", "designation"]);
      const userInfo = omitKeysFromObject(data, [
        "salary",
        "designation",
        "branchId",
      ]);

      if (existingStaff.department === "SUPPORT") {
        return await this.updateSupportStaff(
          id,
          existingStaff,
          staffInfo,
          userInfo,
        );
      }

      return await this.updateInstitutionalStaff(
        id,
        existingStaff,
        staffInfo,
        userInfo,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update staff", error);
      throw error;
    }
  }

  public async getBranchAdminProfile(
    id: Staff["id"],
  ): Promise<BranchAdminProfile> {
    try {
      const admins = await this.db
        .select({
          id: usersTable.id,
          role: roleTable.code,
          email: usersTable.email,
          name: usersTable.name,
          gender: usersTable.gender,
          address: usersTable.address,
          phone: usersTable.phone,
          cnic: usersTable.cnic,
          isActive: usersTable.isActive,
          photo: usersTable.photo,
          isPasswordTemporary: usersTable.isPasswordTemporary,
          createdAt: usersTable.createdAt,
          department: staffTable.department,
          designation: staffTable.designation,
          salary: staffTable.salary,
          type: staffTable.type,
          branch: sql<BranchAdminProfile["branch"]>`json_build_object(
            'id', ${branchTable.id},
            'name', ${branchTable.name},
            'address', ${branchTable.address},
            'email', ${branchTable.email},
            'phone', ${branchTable.phone},
            'isMain', ${branchTable.isMain},
            'isActive', ${branchTable.isActive},
            'instituteId', ${branchTable.instituteId},
            'createdAt', ${branchTable.createdAt}
          )`,
        })
        .from(staffTable)
        .where(eq(staffTable.userId, id))
        .innerJoin(usersTable, eq(staffTable.userId, usersTable.id))
        .innerJoin(roleTable, eq(usersTable.roleId, roleTable.id))
        .innerJoin(branchTable, eq(staffTable.branchId, branchTable.id))
        .execute();

      const branchAdminProfile = admins[0];

      if (!branchAdminProfile) {
        throw new NotFoundException("Staff profile not found");
      }

      return branchAdminProfile;
    } catch (error: unknown) {
      this.logger.error("Failed to get branch admin profile", error);
      throw error;
    }
  }

  public async checkTeacherIsClassTeacher(
    classSectionId: string,
    teacherId: string,
  ): Promise<boolean> {
    try {
      const classTeacher = await this.db.query.classSectionTable.findFirst({
        where: and(
          eq(classSectionTable.id, classSectionId),
          eq(classSectionTable.classTeacherId, teacherId),
        ),
        columns: {
          id: true,
        },
      });
      return Boolean(classTeacher?.id);
    } catch (error) {
      this.logger.error("Failed to check if teacher is class teacher", error);
      throw error;
    }
  }

  public async checkTeacherIsSubjectTeacher(
    teacherId: string,
    options: Pick<SectionSubject, "classSectionId" | "subjectId">,
  ): Promise<boolean> {
    try {
      const subjectTeacher = await this.db.query.sectionSubjectTable.findFirst({
        where: and(
          eq(sectionSubjectTable.classSectionId, options.classSectionId),
          eq(sectionSubjectTable.subjectTeacherId, teacherId),
          eq(sectionSubjectTable.subjectId, options.subjectId),
        ),
        columns: {
          id: true,
        },
      });
      return Boolean(subjectTeacher?.id);
    } catch (error) {
      this.logger.error("Failed to check if teacher is subject teacher", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating support staff members.
  | It updates both the staff table for employment details (salary, designation)
  | and the supportStaffProfile table for personal information within a
  | single database transaction to ensure data consistency.
  |
  */
  private async updateSupportStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    staffAccountInfo: Record<string, unknown>,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction(async pgTrx => {
        if (!existingStaff.supportStaffProfileId) {
          throw new Error("Support staff profile ID not found");
        }

        await pgTrx
          .update(supportStaffProfileTable)
          .set(staffAccountInfo)
          .where(
            eq(
              supportStaffProfileTable.id,
              existingStaff.supportStaffProfileId,
            ),
          )
          .returning();

        const result = await pgTrx
          .update(staffTable)
          .set(staffInfo)
          .where(eq(staffTable.id, id))
          .returning({ id: staffTable.id });

        if (!result[0]) {
          throw new Error("Failed to update staff record");
        }

        return result[0];
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update support staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating institutional staff members
  | (teachers, administrators, accountants). It updates both the staff table
  | for employment details and the users table for personal information
  | within a single database transaction to ensure data consistency.
  |
  */
  private async updateInstitutionalStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    userInfo: Record<string, unknown>,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction(async pgTrx => {
        const updatedStaff = await pgTrx
          .update(staffTable)
          .set(staffInfo)
          .where(eq(staffTable.id, id))
          .returning({ id: staffTable.id });

        if (!updatedStaff[0]) {
          throw new Error("Failed to update staff record");
        }

        if (!existingStaff.userId) {
          throw new Error("User ID not found for institutional staff");
        }

        await pgTrx
          .update(usersTable)
          .set(userInfo)
          .where(eq(usersTable.id, existingStaff.userId));

        return updatedStaff[0];
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update institutional staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create new Staff
  |--------------------------------------------------------------------------
  |
  | This method handles the creation of new staff members for.
  | It supports creating both institutional staff
  | (teachers, administrators, accountants) and support staff. The process
  | includes creating a user account, assigning appropriate roles, generating
  | temporary credentials, and sending login information via email.
  |
  */
  public async create(
    createStaffPayload: CreateStaffPayload,
  ): Promise<EntityId> {
    await this.verifyBranchExists(createStaffPayload.branchId);

    try {
      if (this.isInstitutionalStaff(createStaffPayload)) {
        return await this.createInstitutionalStaff(createStaffPayload);
      }

      return await this.createSupportStaff(createStaffPayload);
    } catch (error: unknown) {
      handleDatabaseInsertOrUpdateException(error, {
        resource: "Staff",
        logger: this.logger,
      });
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of institutional staff members
  | (teachers, administrators, accountants). It creates a user account with
  | the appropriate role, generates a random password, and creates a staff
  | record linked to the user. All operations are performed within a
  | transaction to ensure data consistency.
  |
  */
  private async createInstitutionalStaff(
    createStaffInput: CreateInstitutionStaffPayload,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction(async pgTrx => {
        const staffPersonalInfo =
          this.extractPersonalProperties(createStaffInput);

        const staffRole = await this.rolesService.findByNameOrCreate(
          createStaffInput.type,
          { pgTrx },
        );

        const newUser = await this.userService.create(
          {
            ...staffPersonalInfo,
            roleId: staffRole.id,
          } as NewUser,
          { pgTrx },
        );

        const staffInfo = this.extractStaffProperties(createStaffInput);

        const result = await pgTrx
          .insert(staffTable)
          .values({
            id: newUser.id,
            userId: newUser.id,
            ...staffInfo,
          })
          .returning({ id: staffTable.id });

        if (!result[0]) {
          throw new Error("Failed to create staff record");
        }

        return result[0];
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Institutional Staff By ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves an institutional staff member by their ID.
  | It uses the selectInstitutionalStaffQuery to join the staff and users
  | tables, providing a complete profile of the staff member including
  | both personal and employment information.
  |
  */
  public async findInstitutionalStaffById(
    id: Staff["id"],
    options?: ServiceOptions,
  ): Promise<StaffProfile | undefined> {
    try {
      const result = await this.selectInstitutionalStaffQuery(options)
        .where(eq(staffTable.userId, id))
        .limit(1);
      return result[0] ?? undefined;
    } catch (error: unknown) {
      this.logger.error("Failed to find staff by id", error);
      throw error;
    }
  }

  public async getTeacherProfile(id: Staff["id"]): Promise<TeacherProfile> {
    try {
      const teacher = await this.findInstitutionalStaffById(id);

      if (!teacher) {
        throw new NotFoundException("Teacher not found");
      }

      const activeSession = await this.academicSessionsService.findOne({
        branchId: teacher.branchId,
        isActive: true,
      });

      if (!activeSession) {
        throw new NotFoundException("Active session not found");
      }

      const teacherSections = await this.classSectionsService.find({
        classTeacherId: id,
        academicSessionId: activeSession.id,
      });

      const teacherSubjects = await this.sectionSubjectsService.find({
        subjectTeacherId: id,
        academicSessionId: activeSession.id,
      });

      return {
        ...teacher,
        classTeacherOf: teacherSections,
        subjectTeacherOf: teacherSubjects,
      };
    } catch (error: unknown) {
      this.logger.error("Failed to get teacher profile", error);
      throw error;
    }
  }

  public async verifyClassTeacherExists(
    classTeacherId: string,
    options?: ServiceOptions,
  ) {
    let existingClassTeacher: StaffProfile | undefined;
    try {
      existingClassTeacher = await this.findInstitutionalStaffById(
        classTeacherId,
        options,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to verify class teacher exists", error);
      throw error;
    }

    if (!existingClassTeacher) {
      throw new NotFoundException("Class teacher not found");
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Existing Staff Or Throw
  |--------------------------------------------------------------------------
  |
  | This private method attempts to find a staff member by ID and throws
  | a NotFoundException if the staff member doesn't exist. It's used to
  | validate staff existence before performing operations that require
  | a valid staff record.
  |
  */
  private async findExistingStaffOrThrow(id: Staff["id"]): Promise<Staff> {
    let existingStaff: Staff | undefined;

    try {
      const result = await this.db
        .select()
        .from(staffTable)
        .where(eq(staffTable.id, id))
        .limit(1);
      existingStaff = result[0] ?? undefined;
    } catch (error: unknown) {
      this.logger.error("Failed to find existing staff", error);
      throw error;
    }

    if (!existingStaff) {
      throw new NotFoundException(`Staff with id: ${id} not found`);
    }
    return existingStaff;
  }

  /*
  |--------------------------------------------------------------------------
  | Create Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of support staff members.
  | Unlike institutional staff, support staff don't have user accounts
  | but instead have profiles in the supportStaffProfile table. The method
  | creates both the profile and the staff record within a transaction.
  |
  */
  private async createSupportStaff(
    createStaffInput: CreateStaffPayload,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction(async pgTrx => {
        const createSupportStaffProfileInput =
          this.extractPersonalProperties(createStaffInput);

        const createdSupportStaffProfile = await pgTrx
          .insert(supportStaffProfileTable)
          .values(createSupportStaffProfileInput)
          .returning({
            id: supportStaffProfileTable.id,
            name: supportStaffProfileTable.name,
            email: supportStaffProfileTable.email,
            phone: supportStaffProfileTable.phone,
            address: supportStaffProfileTable.address,
            cnic: supportStaffProfileTable.cnic,
            photo: supportStaffProfileTable.photo,
            gender: supportStaffProfileTable.gender,
          });

        const staffInfo = this.extractStaffProperties(createStaffInput);

        if (!createdSupportStaffProfile[0]) {
          throw new Error("Failed to create support staff profile");
        }

        const result = await pgTrx
          .insert(staffTable)
          .values({
            ...staffInfo,
            supportStaffProfileId: createdSupportStaffProfile[0].id,
          })
          .returning({ id: staffTable.id });

        if (!result[0]) {
          throw new Error("Failed to create staff record");
        }

        return result[0];
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Verify Branch Exists
  |--------------------------------------------------------------------------
  |
  | This private method verifies that a branch with the given ID exists
  | before proceeding with staff operations. It throws a NotFoundException
  | if the branch doesn't exist, ensuring that staff are only created or
  | updated for valid branches.
  |
  */
  private async verifyBranchExists(branchId: string) {
    let existingBranch: BranchResponse | undefined;
    try {
      existingBranch = await this.branchesService.findById(branchId);
    } catch (error) {
      this.logger.error("Failed to verify branch exists", error);
      throw error;
    }

    if (!existingBranch) {
      throw new NotFoundException(`Branch with id: ${branchId} not found`);
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Select Support Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve support staff data by
  | joining the supportStaffProfile and staff tables. It selects all
  | necessary fields from both tables and aliases them appropriately
  | to create a unified staff profile view.
  |
  */
  private selectSupportStaffQuery() {
    return this.db
      .select({
        name: supportStaffProfileTable.name,
        email: supportStaffProfileTable.email,
        phone: supportStaffProfileTable.phone,
        address: supportStaffProfileTable.address,
        gender: supportStaffProfileTable.gender,
        photo: supportStaffProfileTable.photo,
        cnic: supportStaffProfileTable.cnic,
        id: staffTable.id,
        department: staffTable.department,
        designation: staffTable.designation,
        salary: staffTable.salary,
        createdAt: staffTable.createdAt,
        branchId: staffTable.branchId,
        type: staffTable.type,
      })
      .from(supportStaffProfileTable)
      .innerJoin(
        staffTable,
        eq(staffTable.supportStaffProfileId, supportStaffProfileTable.id),
      );
  }

  /*
  |--------------------------------------------------------------------------
  | Select Institutional Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve institutional staff data
  | by joining the staff and users tables. It selects all necessary fields
  | from both tables and aliases them appropriately to create a unified
  | staff profile view for teachers, administrators, and accountants.
  |
  */
  private selectInstitutionalStaffQuery(options?: ServiceOptions) {
    const dbClient = options?.pgTrx ?? this.db;
    return dbClient
      .select({
        name: usersTable.name,
        email: usersTable.email,
        phone: usersTable.phone,
        address: usersTable.address,
        gender: usersTable.gender,
        photo: usersTable.photo,
        cnic: usersTable.cnic,
        id: staffTable.id,
        department: staffTable.department,
        designation: staffTable.designation,
        salary: staffTable.salary,
        createdAt: staffTable.createdAt,
        branchId: staffTable.branchId,
        type: staffTable.type,
      })
      .from(staffTable)
      .innerJoin(usersTable, eq(usersTable.id, staffTable.userId));
  }

  /*
  |--------------------------------------------------------------------------
  | Extract User Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts personal information fields from
  | the staff input payload. These fields are used to create or update
  | user profiles for institutional staff or support staff profiles.
  |
  */
  private extractPersonalProperties<T extends CreateStaffPayload>(
    createStaffInput: T,
  ) {
    const userInfo = extractKeysFromObject(createStaffInput, [
      "name",
      "email",
      "phone",
      "address",
      "gender",
      "photo",
      "cnic",
    ]);
    if (this.isInstitutionalStaff(createStaffInput)) {
      return {
        ...userInfo,
        password: createStaffInput.password,
      };
    }
    return userInfo;
  }

  /*
  |--------------------------------------------------------------------------
  | Extract Staff Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts employment-related fields from
  | the staff input payload. These fields are used to create or update
  | staff records in the staff table, containing information about
  | the staff member's role, department, salary, and designation.
  |
  */
  private extractStaffProperties(createStaffInput: CreateStaffPayload) {
    return extractKeysFromObject(createStaffInput, [
      "salary",
      "department",
      "designation",
      "branchId",
      "type",
    ]);
  }

  private isInstitutionalStaff(
    createStaffInput: CreateStaffPayload | CreateInstitutionStaffPayload,
  ): createStaffInput is CreateInstitutionStaffPayload {
    return createStaffInput.department !== "SUPPORT";
  }
}
