import {
  branchAdminProfileSchema,
  createStaffSchema,
  staffProfileSchema,
  teacherProfileSchema,
  updateStaffSchema,
} from "../dto/staff.dto.js";
import { z } from "zod";
import { listStaffQuerySchema } from "../dto/staff-query.dto.js";
import { staffTable } from "../../../../db/schemas/index.js";

export type NewStaff = typeof staffTable.$inferInsert;
export type Staff = typeof staffTable.$inferSelect;
export type StaffUPdate = Partial<NewStaff>;

export type CreateStaffPayload = z.infer<typeof createStaffSchema> & {
  branchId: string;
};

export type CreateInstitutionStaffPayload = Omit<
  CreateStaffPayload,
  "password"
> & {
  password: string;
};

export type UpdateStaffPayload = z.infer<typeof updateStaffSchema> & {
  branchId: string;
};

export type StaffProfile = z.infer<typeof staffProfileSchema>;

export type BranchAdminProfile = z.infer<typeof branchAdminProfileSchema>;

export type TeacherProfile = z.infer<typeof teacherProfileSchema>;

// Query
export type ListStaffQueryOptions = z.infer<typeof listStaffQuerySchema>;
