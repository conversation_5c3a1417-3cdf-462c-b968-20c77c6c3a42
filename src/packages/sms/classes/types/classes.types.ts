import { classResponseSchema, createClassSchema } from "../dto/classes.dto.js";
import { z } from "zod";
import { classTable } from "../../../../db/schemas/index.js";

export type Class = typeof classTable.$inferSelect;
export type NewClass = typeof classTable.$inferInsert;
export type ClassUpdate = Partial<NewClass>;

export type CreateClassPayload = z.infer<typeof createClassSchema> & {
  academicSessionId: string;
};

export type UpdateClassPayload = Partial<CreateClassPayload>;

export type ClassResponse = z.infer<typeof classResponseSchema>;
